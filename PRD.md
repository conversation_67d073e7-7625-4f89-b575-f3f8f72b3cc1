# Roulette Tracker Application - Product Requirements Document (PRD)

## 1. Technical Architecture

### 1.1 System Overview
The Roulette Tracker is a real-time web application built with Flask and SocketIO that enables collaborative tracking and analysis of roulette wheel numbers. The system supports both American (38 numbers: 0, 00, 1-36) and European (37 numbers: 0, 1-36) roulette wheels.

### 1.2 Technology Stack
- **Backend Framework**: Flask 2.0+
- **Real-time Communication**: Flask-SocketIO 5.0+ with eventlet
- **Frontend**: Vanilla JavaScript with SocketIO client
- **Styling**: CSS3 with custom properties
- **Session Management**: Flask sessions with secure cookies
- **Authentication**: Werkzeug password hashing
- **Logging**: Python logging with rotating file handlers
- **Deployment**: Systemd service with virtual environment

### 1.3 File Structure
```
app_1/
├── app.py                          # Main Flask application
├── audit_logger.py                 # Custom audit logging system
├── requirements.txt                # Python dependencies
├── app_1.service                   # Systemd service configuration
├── templates/
│   ├── index.html                  # Landing page for code entry
│   ├── admin_login.html            # Admin authentication page
│   ├── admin_dashboard.html        # Admin control interface
│   └── user_portal.html            # User number submission interface
├── static/
│   ├── css/
│   │   └── style.css               # Complete application styling
│   └── js/
│       └── main.js                 # Frontend JavaScript logic
├── logs/
│   └── audit.log                   # Security and activity logs
├── session_history/
│   ├── session_history_metadata.json  # Session metadata index
│   └── session_*.txt               # Individual session history files
└── venv/                           # Python virtual environment
```

### 1.4 Core Dependencies
- Flask>=2.0: Web framework
- Flask-SocketIO>=5.0: WebSocket communication
- Werkzeug>=2.0: WSGI utilities and security
- eventlet: Async networking library
- python-dotenv: Environment variable management

### 1.5 Network Architecture
- **Port**: 8081 (configurable)
- **Protocol**: HTTP/HTTPS with WebSocket upgrade
- **CORS**: Configured for cross-origin requests
- **Transport**: Polling fallback for WebSocket compatibility

## 2. Feature Specifications

### 2.1 Core Roulette Tracking Features

#### 2.1.1 Number Entry and Validation
**User Story**: As an admin or user, I want to enter roulette numbers so that they can be tracked and analyzed.

**Acceptance Criteria**:
- Accept single numbers (e.g., "7", "0", "00")
- Accept multiple numbers with hyphen separation (e.g., "7-15-0")
- Validate numbers against selected wheel type (American/European)
- Provide immediate feedback for invalid entries
- Support comma-to-hyphen conversion for user convenience
- Maintain chronological order of entries

**Implementation Details**:
- Input validation using `RouletteTracker.valid_numbers` set
- Multi-number processing via string splitting on hyphens
- Real-time validation with descriptive error messages
- History maintained as ordered list in `RouletteTracker.history`

#### 2.1.2 Wheel Type Management
**User Story**: As an admin, I want to switch between American and European roulette wheels so that tracking matches the actual game being played.

**Acceptance Criteria**:
- Support American wheel (0, 00, 1-36) with 38 positions
- Support European wheel (0, 1-36) with 37 positions
- Maintain separate wheel sequences for distance calculations
- Reset session data when switching wheel types
- Save current session before switching
- Update UI to reflect current wheel type

**Implementation Details**:
- Predefined wheel sequences in `RouletteTracker` class
- American sequence: ['0', '28', '9', '26', '30', '11', '7', '20', '32', '17', '5', '22', '34', '15', '3', '24', '36', '13', '1', '00', '27', '10', '25', '29', '12', '8', '19', '31', '18', '6', '21', '33', '16', '4', '23', '35', '14', '2']
- European sequence: ['0', '32', '15', '19', '4', '21', '2', '25', '17', '34', '6', '27', '13', '36', '11', '30', '8', '23', '10', '5', '24', '16', '33', '1', '20', '14', '31', '9', '22', '18', '29', '7', '28', '12', '35', '3', '26']

#### 2.1.3 Distance Calculation System
**User Story**: As the system, I need to calculate distances between consecutive numbers on the wheel so that analytical algorithms can identify patterns.

**Acceptance Criteria**:
- Calculate clockwise distance between consecutive numbers
- Maintain deque of distances for efficient access
- Handle wrap-around calculations (last position to first)
- Update distance tracking with each new number entry
- Support undo operations that recalculate distances

**Implementation Details**:
- Distance formula: `(current_index - last_index + num_slots) % num_slots`
- Distances stored in `collections.deque` for O(1) operations
- Automatic recalculation during undo operations
- Error handling for invalid wheel positions

#### 2.1.4 Greedy Hitting Set Algorithm
**User Story**: As the system, I need to compute optimal number sets that cover all consecutive number pairs so that users can identify strategic betting opportunities.

**Acceptance Criteria**:
- Generate hitting sets that cover all consecutive number pairs (duplexes)
- Use greedy algorithm with randomization for optimization
- Run multiple iterations (1300 by default) to find minimal sets
- Categorize results by dozens (D1: 1-12, D2: 13-24, D3: 25-36, Zs: 0/00)
- Provide deterministic fallback when randomization disabled

**Implementation Details**:
- Algorithm in `compute_greedy_hitting_set()` function
- Duplex generation from consecutive number pairs
- Weighted selection based on frequency and uniqueness scores
- Multiple optimization runs in `compute_best_greedy_hitting_set()`
- Categorization via `categorize_numbers_for_display()`

#### 2.1.5 Distilled Output Calculation
**User Story**: As an admin, I want to see numbers that are in the hitting set but not in recent unhit numbers so that I can identify least likely outcomes.

**Acceptance Criteria**:
- Calculate difference between hitting set and tracker unhit numbers
- Display results categorized by dozens and zeros
- Show count of distilled numbers
- Track previous distilled output for comparison
- Use for win/loss streak calculations

**Implementation Details**:
- Function: `calculate_distilled_output(tracker_unhit_cat, hitting_set_cat)`
- Filters hitting set numbers not present in tracker unhit categories
- Returns categorized dictionary and total count
- Stored in session state for persistence

#### 2.1.6 Undo Functionality
**User Story**: As an admin or user, I want to undo the last number entry so that I can correct mistakes without restarting the session.

**Acceptance Criteria**:
- Remove last number from history
- Recalculate all distance tracking from scratch
- Maintain data integrity during rebuild process
- Handle edge cases (empty history, single entry)
- Provide feedback on successful/failed undo operations

**Implementation Details**:
- Method: `RouletteTracker.undo_last_spin()`
- Complete state rebuild from remaining history
- Temporary storage during recalculation
- Rollback protection on calculation errors
- Reset to initial state when history becomes empty

### 2.2 Session Management Features

#### 2.2.1 Session Creation and Lifecycle
**User Story**: As an admin, I want to create and manage tracking sessions so that I can organize different gaming periods.

**Acceptance Criteria**:
- Create new session on admin login
- Assign unique session identifier
- Track session metadata (username, creation time, wheel type)
- Maintain session state across WebSocket connections
- Handle session timeout and cleanup
- Support session reset with history preservation

**Implementation Details**:
- Session ID generation using `secrets.token_hex(16)`
- Storage in `active_sessions` dictionary
- Timeout handling with `SESSION_INACTIVITY_TIMEOUT` (2 hours)
- Automatic cleanup via background thread
- State includes tracker instance, user connections, statistics

#### 2.2.2 Session History Persistence
**User Story**: As an admin, I want to save and retrieve session histories so that I can review past gaming sessions.

**Acceptance Criteria**:
- Automatically save session history on logout/reset
- Generate unique filenames with timestamps
- Store session metadata in JSON index
- Support viewing saved session contents
- Enable deletion of old sessions
- Limit stored sessions to prevent disk overflow

**Implementation Details**:
- Filename format: `session_{username}_{timestamp}_{session_id}.txt`
- Content format: Hyphen-separated number sequence
- Metadata in `session_history_metadata.json`
- Maximum 100 saved sessions (`MAX_SAVED_SESSIONS_METADATA`)
- File validation and security checks for access

#### 2.2.3 Statistical Tracking
**User Story**: As an admin, I want to track win/loss streaks and performance metrics so that I can analyze the effectiveness of the distilled output predictions.

**Acceptance Criteria**:
- Track current and maximum win/loss streaks
- Calculate average numbers between wins
- Monitor numbers since last match
- Update statistics with each number entry
- Reset statistics on session reset or wheel change
- Display real-time statistics in dashboard

**Implementation Details**:
- Functions: `check_distill_match()`, `update_win_loss_streaks()`
- Match detection against previous distilled output
- Statistics stored in admin session data
- Real-time updates via WebSocket events
- Automatic reset on session state changes

### 2.3 Real-time Collaboration Features

#### 2.3.1 Multi-user Session Support
**User Story**: As an admin, I want to allow multiple users to join my session so that they can contribute number entries collaboratively.

**Acceptance Criteria**:
- Generate one-time access codes for users
- Support multiple simultaneous user connections
- Track connected user count and identities
- Broadcast session updates to all participants
- Handle user disconnections gracefully
- Notify admin of user join/leave events

**Implementation Details**:
- One-time codes with expiration (`ONE_TIME_CODE_VALIDITY`: 10 minutes)
- User tracking in `admin_s_data["user_connections"]` set
- WebSocket room management for broadcast communication
- Automatic cleanup of expired codes and disconnected users

#### 2.3.2 Real-time Data Synchronization
**User Story**: As a user or admin, I want to see live updates of session data so that all participants have current information.

**Acceptance Criteria**:
- Broadcast number entries to all session participants
- Update dashboard displays in real-time
- Synchronize session state across all connections
- Handle connection interruptions gracefully
- Provide connection status indicators
- Support automatic reconnection

**Implementation Details**:
- WebSocket events: `session_update`, `user_joined_notification`, `user_left_notification`
- State synchronization via `get_admin_dashboard_state()`
- Connection status tracking and display
- Automatic state refresh on reconnection

### 2.4 Text-to-Speech Features

#### 2.4.1 Distilled Output Announcement
**User Story**: As an admin, I want distilled output numbers to be announced audibly so that I can focus on the game without looking at the screen.

**Acceptance Criteria**:
- Automatically announce new distilled output numbers
- Support speech rate adjustment (0.5x to 2.0x)
- Enable mute/unmute functionality
- Allow repetition of last announcement
- Configure number of repetitions (1-3 times)
- Handle special pronunciation for "00" (zero zero)

**Implementation Details**:
- Web Speech API integration in frontend JavaScript
- Rate control with 0.1 increments
- Number formatting for proper pronunciation
- Speech synthesis warmup to prevent volume lag
- Configurable repetition settings

#### 2.4.2 Speech Control Interface
**User Story**: As an admin, I want comprehensive speech controls so that I can customize audio feedback to my preferences.

**Acceptance Criteria**:
- Stop current speech immediately
- Mute/unmute toggle with visual feedback
- Repeat last spoken numbers on demand
- Adjust speech rate with visual indicator
- Select repetition count (1-3 times)
- Maintain speech settings across page refreshes

**Implementation Details**:
- Control buttons: Stop, Mute, Repeat, Slower, Faster
- Rate display with current value
- Repetition dropdown selection
- Local storage for preference persistence
- Visual feedback for active states

## 3. User Management System

### 3.1 Authentication Architecture

#### 3.1.1 Admin Authentication
**User Story**: As an admin, I want secure login access so that I can control roulette tracking sessions.

**Acceptance Criteria**:
- Username/password authentication
- Password hashing using Werkzeug security
- Session-based authentication with secure cookies
- Rate limiting on login attempts (5 attempts per 5 minutes)
- Automatic session timeout after 2 hours of inactivity
- Secure logout with session cleanup

**Implementation Details**:
- Admin credentials stored in `admin_users` dictionary
- Password hashing: `generate_password_hash()`, verification: `check_password_hash()`
- Session management via Flask sessions and `active_sessions` dictionary
- Rate limiting per IP address with configurable windows
- Automatic cleanup of expired sessions via background thread

#### 3.1.2 User Access Control
**User Story**: As a user, I want to join admin sessions using access codes so that I can participate in number tracking.

**Acceptance Criteria**:
- One-time access codes generated by admin
- Code expiration after 10 minutes
- Single-use code consumption
- Validation against active admin sessions
- Automatic cleanup of used/expired codes
- Special "admin" code for admin login redirect

**Implementation Details**:
- Code generation: 8-character alphanumeric strings
- Storage in `one_time_codes` dictionary with expiration timestamps
- Validation includes session existence check
- Automatic cleanup via periodic background task
- Rate limiting on code generation (10 codes per minute)

### 3.2 Session Security

#### 3.2.1 Session Token Management
**User Story**: As the system, I need secure session management so that user sessions cannot be hijacked or forged.

**Acceptance Criteria**:
- Generate cryptographically secure session tokens
- Use secure cookie settings (SameSite=Lax)
- Implement session timeout with automatic cleanup
- Validate session tokens on each request
- Handle concurrent sessions appropriately
- Secure session storage and transmission

**Implementation Details**:
- Session ID generation: `secrets.token_hex(16)` (32 characters)
- Flask session configuration with secure secret key
- Session validation via `get_session_if_valid()` function
- Timeout enforcement with `SESSION_INACTIVITY_TIMEOUT`
- Activity timestamp updates on valid requests

#### 3.2.2 Rate Limiting and Abuse Prevention
**User Story**: As the system, I need to prevent abuse and brute force attacks so that the application remains available and secure.

**Acceptance Criteria**:
- Limit login attempts per IP address
- Limit code generation requests per IP
- Implement sliding window rate limiting
- Provide appropriate error messages for rate limits
- Log security events for monitoring
- Automatic cleanup of rate limit data

**Implementation Details**:
- Rate limit storage: `rate_limit_store` dictionary by IP
- Login limits: 5 attempts per 5 minutes
- Code generation limits: 10 requests per minute
- Sliding window implementation with timestamp tracking
- Security event logging via audit system

## 4. Data Models

### 4.1 Core Data Structures

#### 4.1.1 RouletteTracker Class
```python
class RouletteTracker:
    def __init__(self, wheel_type='american'):
        self.wheel_type: str                    # 'american' or 'european'
        self.american_wheel_sequence: List[str] # 38-position wheel layout
        self.european_wheel_sequence: List[str] # 37-position wheel layout
        self.wheel_sequence: List[str]          # Active wheel sequence
        self.num_slots: int                     # 38 for American, 37 for European
        self.valid_numbers: Set[str]            # Valid numbers for current wheel
        self.spin_distances: deque              # Distance tracking between numbers
        self.last_spin: Optional[str]           # Last entered number
        self.history: List[str]                 # Chronological number history
```

**Methods**:
- `update_spin(spin_number_str)`: Add number(s) to tracking
- `undo_last_spin()`: Remove last entry and rebuild state
- `reset_tracker_and_history()`: Clear all tracking data
- `entry_count()`: Return number of entries
- `get_unhit_numbers()`: Calculate unhit number categories

#### 4.1.2 Session Data Structure
```python
active_sessions = {
    session_id: {
        "username": str,                        # Admin username
        "role": str,                           # "admin"
        "last_activity": datetime,             # UTC timestamp
        "wheel_type": str,                     # Current wheel type
        "tracker_instance": RouletteTracker,   # Tracking instance
        "user_connections": Set[str],          # Connected user socket IDs
        "user_sids_map": Dict[str, str],       # Socket ID to temp user ID mapping
        "current_save_file_display_name": str, # Display name for saves
        "spins_since_last_match": int,         # Numbers since last distill match
        "current_win_streak": int,             # Current consecutive wins
        "current_loss_streak": int,            # Current consecutive losses
        "max_win_streak": int,                 # Maximum win streak achieved
        "max_loss_streak": int,                # Maximum loss streak achieved
        "total_wins": int,                     # Total wins in session
        "sum_of_spins_between_wins": int,      # Sum for average calculation
        "previous_distill_output_state": Dict, # Previous distill for comparison
        "prev_best_set_for_match_logic": Set   # Previous hitting set
    }
}
```

#### 4.1.3 One-Time Code Structure
```python
one_time_codes = {
    code: {
        "session_id": str,          # Associated admin session ID
        "expires_at": datetime,     # UTC expiration timestamp
        "admin_username": str,      # Admin who generated the code
        "used": bool               # Whether code has been consumed
    }
}
```

#### 4.1.4 Session History Metadata
```python
{
    "sessions": [
        {
            "id": str,              # Original session ID
            "admin": str,           # Admin username
            "timestamp": str,       # ISO format timestamp
            "filename": str,        # Unique filename
            "spin_count": int,      # Number of entries in session
            "wheel_type": str       # Wheel type used
        }
    ]
}
```

### 4.2 Data Relationships

#### 4.2.1 Session to User Mapping
- One admin session can have multiple user connections
- Each user connection tracked by WebSocket session ID
- Temporary user IDs generated for display purposes
- User connections automatically cleaned up on disconnect

#### 4.2.2 Session to History Mapping
- Each admin session can generate multiple history files
- History files created on logout, reset, or wheel change
- Metadata index maintains searchable session information
- File naming ensures uniqueness across time and users

#### 4.2.3 Code to Session Mapping
- One-time codes linked to specific admin sessions
- Codes become invalid if admin session expires
- Multiple codes can be generated for same session
- Used codes maintained until cleanup for audit purposes

## 5. API Documentation

### 5.1 HTTP REST Endpoints

#### 5.1.1 Authentication Endpoints

**POST /admin-login**
- **Purpose**: Authenticate admin user
- **Request Body**: Form data with username and password
- **Response**: JSON with success status and session information
- **Rate Limiting**: 5 attempts per 5 minutes per IP
- **Error Codes**: 401 (invalid credentials), 429 (rate limited)

```json
// Success Response
{
    "success": true,
    "session_id": "32-character-hex-string",
    "username": "admin_username",
    "role": "admin"
}

// Error Response
{
    "success": false,
    "message": "Invalid username or password."
}
```

**POST /api/logout**
- **Purpose**: Terminate admin session
- **Authentication**: Requires valid session
- **Response**: JSON confirmation
- **Side Effects**: Cleanup session, notify connected users, save history

```json
{
    "success": true,
    "message": "Logged out."
}

#### 5.1.2 Code Management Endpoints

**POST /api/generate-code**
- **Purpose**: Generate one-time access code for users
- **Authentication**: Requires admin session
- **Rate Limiting**: 10 requests per minute per IP
- **Response**: JSON with code and expiration information

```json
// Success Response
{
    "success": true,
    "code": "8-character-code",
    "expires_in_seconds": 600
}
```

**POST /api/validate-code**
- **Purpose**: Validate user access code
- **Request Body**: JSON with code
- **Response**: JSON with validation result and session information
- **Special Cases**: "admin" code redirects to admin login

```json
// Success Response
{
    "success": true,
    "message": "Code valid.",
    "admin_session_id": "session-id",
    "admin_username": "admin-name",
    "action": "proceed_to_user_portal"
}

// Admin Redirect Response
{
    "success": true,
    "action": "redirect_admin_login"
}
```

#### 5.1.3 Session History Endpoints

**GET /api/admin/history/list**
- **Purpose**: Retrieve list of saved session histories
- **Authentication**: Requires admin session
- **Response**: JSON array of session metadata

```json
{
    "success": true,
    "histories": [
        {
            "id": "session-id",
            "admin": "username",
            "timestamp": "2024-01-01T12:00:00Z",
            "filename": "session_user_20240101_120000_123456_abc123.txt",
            "spin_count": 45,
            "wheel_type": "american"
        }
    ]
}
```

**GET /api/admin/history/view/{filename}**
- **Purpose**: Retrieve content of specific session history
- **Authentication**: Requires admin session
- **Path Parameter**: filename (validated for security)
- **Response**: JSON with file content

```json
{
    "success": true,
    "filename": "session_file.txt",
    "content": "7-15-0-23-11-8"
}
```

**DELETE /api/admin/history/delete/{filename}**
- **Purpose**: Delete specific session history file
- **Authentication**: Requires admin session
- **Path Parameter**: filename (validated for security)
- **Response**: JSON confirmation
- **Side Effects**: Removes file and updates metadata index

### 5.2 WebSocket Events

#### 5.2.1 Connection Management Events

**connect**
- **Direction**: Client → Server (automatic)
- **Purpose**: Establish WebSocket connection
- **Response**: `connection_ack` event with session ID

**disconnect**
- **Direction**: Client → Server (automatic)
- **Purpose**: Handle client disconnection
- **Side Effects**: Remove from user connections, notify admin

**join_admin_session**
- **Direction**: Admin Client → Server
- **Payload**: `{session_id: string}`
- **Purpose**: Join admin to their session room
- **Response**: `session_update` with current state

**join_user_to_admin_session**
- **Direction**: User Client → Server
- **Payload**: `{code: string, admin_session_id: string}`
- **Purpose**: Join user to admin session
- **Response**: `user_join_success` or `user_join_failed`

#### 5.2.2 Admin Action Events

**admin_action**
- **Direction**: Admin Client → Server
- **Purpose**: Execute admin commands
- **Payload Variants**:

```javascript
// Submit number(s)
{
    action_type: "submit_spin",
    spin_value: "7" | "7-15-0"
}

// Undo last entry
{
    action_type: "undo_spin"
}

// Save current session
{
    action_type: "save_session"
}

// Reset session (save and clear)
{
    action_type: "reset_session"
}

// Change save context name
{
    action_type: "change_file",
    filename_base: "new-context-name"
}
```

**admin_change_wheel_type**
- **Direction**: Admin Client → Server
- **Payload**: `{wheel_type: "american" | "european"}`
- **Purpose**: Change wheel type and reset session
- **Side Effects**: Save current session, reset tracker, update all clients

#### 5.2.3 User Action Events

**user_submit_spin**
- **Direction**: User Client → Server
- **Payload**: `{admin_session_id: string, spin_value: string}`
- **Purpose**: Submit number(s) to admin session
- **Validation**: User must be connected to specified session

**user_undo_last_spin**
- **Direction**: User Client → Server
- **Payload**: `{admin_session_id: string}`
- **Purpose**: Undo last entry in admin session
- **Validation**: User must be connected to specified session

#### 5.2.4 Server Response Events

**session_update**
- **Direction**: Server → All Session Clients
- **Purpose**: Broadcast current session state
- **Payload**: Complete session state object (see Data Models)

**action_feedback**
- **Direction**: Server → Client
- **Payload**: `{message: string, type: "success" | "error" | "info" | "success_major"}`
- **Purpose**: Provide user feedback for actions

**error_toast**
- **Direction**: Server → Client
- **Payload**: `{message: string}`
- **Purpose**: Display error message to user

**error_critical**
- **Direction**: Server → Client
- **Payload**: `{message: string}`
- **Purpose**: Handle critical errors requiring reconnection

**user_joined_notification**
- **Direction**: Server → Admin Client
- **Payload**: `{temp_user_id: string, user_count: number}`
- **Purpose**: Notify admin of new user connection

**user_left_notification**
- **Direction**: Server → Admin Client
- **Payload**: `{temp_user_id: string, user_count: number}`
- **Purpose**: Notify admin of user disconnection

**admin_terminated_session**
- **Direction**: Server → User Clients
- **Payload**: `{message: string}`
- **Purpose**: Notify users that admin ended session

## 6. User Interface Specifications

### 6.1 Design System

#### 6.1.1 Color Palette
```css
:root {
    --primary-color: #FF6F61;      /* Main orange/coral */
    --secondary-color: #87CEEB;    /* Light blue */
    --accent-color: #FFD700;       /* Gold accent */
    --text-color: #2c3e50;         /* Dark text */
    --text-light: #555e68;         /* Light text */
    --bg-color: #F4F7F6;           /* Background */
    --card-bg: #FFFFFF;            /* Card background */
    --border-color: #E0E0E0;       /* Borders */
    --success-bg: #E6FFFA;         /* Success background */
    --success-text: #00A78E;       /* Success text */
    --error-bg: #FFF0F0;           /* Error background */
    --error-text: #D8000C;         /* Error text */
    --info-bg: #EBF8FF;            /* Info background */
    --info-text: #007BFF;          /* Info text */
}
```

#### 6.1.2 Typography
- **Font Family**: Inter, system fonts fallback
- **Font Weights**: 300 (light), 400 (regular), 500 (medium), 600 (semibold), 700 (bold)
- **Base Font Size**: 16px
- **Line Height**: 1.65
- **Heading Scales**: H1 (1.8-2.2em), H2 (1.3-1.6em), H3 (1.1-1.3em)

#### 6.1.3 Layout System
- **Border Radius**: 8px
- **Box Shadow**: `0 4px 12px rgba(0, 0, 0, 0.05)`
- **Hover Shadow**: `0 6px 16px rgba(0, 0, 0, 0.08)`
- **Card Padding**: 25px 30px
- **Responsive Breakpoints**: Mobile-first design with clamp() functions

### 6.2 Page Layouts

#### 6.2.1 Landing Page (/)
**Purpose**: Entry point for users to input access codes

**Layout Structure**:
```html
<body class="auth-body">
    <div class="auth-container card">
        <h1>Enter Code</h1>
        <form id="user-join-form">
            <div class="input-group">
                <label for="one-time-code">Access Code</label>
                <input type="text" id="one-time-code" maxlength="8"
                       placeholder="Enter code here">
            </div>
            <button type="submit" class="button-primary button-full-width">
                Proceed
            </button>
        </form>
        <div id="user-join-message-area" class="message-box"></div>
    </div>
</body>
```

**Interactive Elements**:
- Code input field with 8-character limit
- Submit button with full-width styling
- Message area for feedback
- Special handling for "admin" code (redirects to admin login)

**Responsive Behavior**:
- Centered layout on all screen sizes
- Maximum width: 450px
- Vertical centering with flexible top margin
- Touch-friendly input sizing

#### 6.2.2 Admin Login Page (/admin-login)
**Purpose**: Authentication interface for administrators

**Layout Structure**:
```html
<body class="auth-body">
    <div class="auth-container card">
        <h1>Admin Login</h1>
        <form id="admin-login-form">
            <div class="input-group">
                <label for="username">Username</label>
                <input type="text" id="username" autocomplete="username">
            </div>
            <div class="input-group">
                <label for="password">Password</label>
                <input type="password" id="password" autocomplete="current-password">
            </div>
            <button type="submit" class="button-primary button-full-width">
                Login
            </button>
        </form>
        <div id="login-message-area" class="message-box"></div>
        <p class="auth-switch">
            User? <a href="/">Enter code here</a>.
        </p>
    </div>
</body>
```

**Interactive Elements**:
- Username and password fields with autocomplete
- Login button with loading states
- Error message display area
- Link to user code entry page

**Security Features**:
- Password field masking
- Form validation
- Rate limiting feedback
- Secure credential transmission

#### 6.2.3 Admin Dashboard (/admin-dashboard)
**Purpose**: Main control interface for session management

**Layout Structure**:
```html
<body class="admin-dashboard-body">
    <div class="admin-container">
        <header class="dashboard-header">
            <!-- Admin info and logout -->
        </header>

        <div class="dashboard-focused-view">
            <!-- Mobile-optimized key information -->
            <section class="status-panel card">
                <!-- Tracker status display -->
            </section>
            <section class="output-panel card current-distill-panel">
                <!-- Current distilled output -->
            </section>
            <section class="tts-controls card">
                <!-- Speech controls -->
            </section>
            <section class="output-panel card previous-distill-panel">
                <!-- Previous distilled output -->
            </section>
        </div>

        <div class="dashboard-main-grid">
            <div class="dashboard-column-left">
                <section class="control-panel card">
                    <!-- Session controls -->
                </section>
                <section class="wheel-config-panel card">
                    <!-- Wheel configuration -->
                </section>
            </div>
            <div class="dashboard-column-right">
                <section class="output-display card">
                    <!-- Number history display -->
                </section>
                <section class="history-management-panel card">
                    <!-- Session history management -->
                </section>
            </div>
        </div>
    </div>
</body>
```

**Key Components**:

**Header Section**:
- Welcome message with admin username
- Session ID display (truncated)
- Connected user count
- Current wheel type indicator
- Logout button

**Status Panel**:
- Color-coded status indicator (RED/YELLOW/GREEN)
- Entry count display
- Last number entered
- Numbers since last match
- Maximum win/loss streaks
- Average numbers between wins
- Save context display

**Distilled Output Panels**:
- Current distilled output (primary focus)
- Previous distilled output (comparison)
- Categorized display (Zs, D1, D2, D3)
- Number count indicators

**Text-to-Speech Controls**:
- Stop, Mute, Repeat buttons
- Speech rate adjustment (Slower/Faster)
- Rate display (0.5x to 2.0x)
- Repetition count selector (1-3 times)

**Session Controls**:
- User code generation with copy functionality
- Number entry input field
- Submit, Undo, Save, Reset buttons
- File context change functionality

**Wheel Configuration**:
- Wheel type selector (American/European)
- Apply button with confirmation dialog
- Reset warning messages

**History Display**:
- Chronological number list
- Entry count indicator
- Hyphen-separated format

**History Management**:
- Saved session list with metadata
- View/Delete functionality
- Content display area
- Refresh controls

#### 6.2.4 User Portal (/user-portal)
**Purpose**: Simplified interface for users to submit numbers

**Layout Structure**:
```html
<body class="user-portal-body">
    <div class="user-container card">
        <header class="portal-header">
            <h1>User Portal</h1>
            <div class="connection-status-container">
                <span>Status:</span>
                <div id="user-connection-status-indicator"
                     class="connection-indicator status-disconnected">
                </div>
                <span id="user-connection-admin-name"></span>
            </div>
        </header>

        <section class="user-actions">
            <h2>Submit Your Numbers</h2>
            <div class="input-group">
                <label for="user-numbers-input">Number(s)</label>
                <input type="text" id="user-numbers-input"
                       placeholder="e.g., 7-15-0">
            </div>
            <div class="button-group">
                <button id="user-submit-numbers-button"
                        class="button-success">Submit Numbers</button>
                <button id="user-undo-entry-button"
                        class="button-warning">Undo Last Entry</button>
            </div>
        </section>

        <div id="user-portal-message-area" class="message-box"></div>
    </div>
</body>
```

**Interactive Elements**:
- Connection status indicator with color coding
- Number input field with format examples
- Submit and undo buttons
- Real-time feedback messages
- Automatic comma-to-hyphen conversion

**Connection States**:
- **Connecting**: Amber indicator, "Connecting..." status
- **Connected**: Green indicator, "Connected" status
- **Disconnected**: Red indicator, disabled controls

### 6.3 Component Specifications

#### 6.3.1 Status Indicators
**Color Coding**:
- **RED**: ≤70 entries (high priority)
- **YELLOW**: 71-90 entries (medium priority)
- **GREEN**: >90 entries (low priority)
- **NEUTRAL**: Default/inactive state

**Visual Design**:
- Pill-shaped badges with rounded corners
- Bold text with contrasting colors
- Consistent sizing across contexts
- Hover effects for interactive elements

#### 6.3.2 Button System
**Button Types**:
- **Primary**: Orange background (#FF6F61), white text
- **Secondary**: Light blue background (#87CEEB), dark text
- **Success**: Teal background (#00A78E), white text
- **Warning**: Orange background (#FFA500), white text
- **Danger**: Red background, white text
- **TTS**: Specialized speech control styling

**Interactive States**:
- **Hover**: Slight upward translation (-2px), enhanced shadow
- **Active**: Inset shadow, no translation
- **Disabled**: Gray background, reduced opacity, no interactions
- **Loading**: Spinner or text change for async operations

#### 6.3.3 Message System
**Message Types**:
- **Success**: Green background, checkmark icon, auto-dismiss (7s)
- **Error**: Red background, error icon, manual dismiss
- **Info**: Blue background, info icon, auto-dismiss (7s)
- **Warning**: Orange background, warning icon, manual dismiss

**Display Behavior**:
- Slide-in animation from top
- Consistent positioning across pages
- Clear typography with adequate contrast
- Responsive sizing for mobile devices

#### 6.3.4 Form Controls
**Input Fields**:
- Consistent padding (14px 18px)
- Border styling with focus states
- Placeholder text with reduced opacity
- Validation feedback integration
- Autocomplete attributes for accessibility

**Focus Management**:
- Visible focus indicators
- Logical tab order
- Enter key submission support
- Escape key for dismissal

### 6.4 Responsive Design

#### 6.4.1 Mobile Optimization
**Dashboard Focused View**:
- Priority information displayed first
- Collapsible sections for secondary content
- Touch-friendly button sizing (minimum 44px)
- Optimized text sizing with clamp() functions

**Navigation Patterns**:
- Single-column layout on mobile
- Sticky header with essential information
- Scroll-based content discovery
- Gesture-friendly interactions

#### 6.4.2 Tablet and Desktop
**Multi-column Layouts**:
- Two-column grid for dashboard content
- Flexible column sizing based on content
- Sidebar navigation for secondary functions
- Expanded control panels with more detail

**Enhanced Interactions**:
- Hover effects for desktop users
- Keyboard shortcuts for power users
- Context menus for advanced functions
- Drag-and-drop capabilities where appropriate

## 7. Business Logic

### 7.1 Core Algorithms

#### 7.1.1 Greedy Hitting Set Algorithm
**Purpose**: Find minimal set of numbers that "hit" (cover) all consecutive number pairs in the history.

**Algorithm Steps**:
1. Generate all consecutive number pairs (duplexes) from history
2. Create mapping of numbers to duplexes they appear in
3. Calculate coverage scores for each number
4. Iteratively select numbers with highest coverage
5. Remove covered duplexes from consideration
6. Repeat until all duplexes are covered

**Optimization Strategy**:
- Run algorithm 1300 times with randomization
- Use weighted selection based on frequency and uniqueness
- Select smallest hitting set from all runs
- Fallback to deterministic selection for ties

**Mathematical Foundation**:
```python
def compute_greedy_hitting_set(spins_list_str, randomize=True):
    # Generate consecutive pairs (duplexes)
    duplexes = [tuple(spins[i:i+2]) for i in range(len(spins) - 1)]

    # Map each number to duplexes it appears in
    spin_to_duplexes = {spin: set() for spin in unique_spins}
    for idx, duplex in enumerate(duplexes):
        for spin_in_duplex in set(duplex):
            spin_to_duplexes[spin_in_duplex].add(idx)

    # Calculate uniqueness scores
    duplex_cover_ct = [coverage_count_for_duplex_idx]
    spin_uniq_score = {
        s: sum(1.0 / max(1, duplex_cover_ct[idx])
               for idx in spin_to_duplexes[s])
    }

    # Greedy selection with optional randomization
    while uncovered_indices and candidates:
        # Select number with maximum coverage
        # Use weighted random selection if randomize=True
        # Remove covered duplexes and continue
```

#### 7.1.2 Distance Calculation System
**Purpose**: Track spatial relationships between consecutive numbers on the roulette wheel.

**Distance Formula**:
```python
distance = (current_index - last_index + num_slots) % num_slots
```

**Wheel Sequences**:
- **American**: 38 positions with specific physical layout
- **European**: 37 positions with different physical layout
- **Clockwise Direction**: Consistent measurement direction

**Distance Tracking**:
- Maintain deque of recent distances for pattern analysis
- Use for unhit number calculation
- Support undo operations with complete recalculation

#### 7.1.3 Unhit Number Calculation
**Purpose**: Identify numbers that haven't appeared in recent distance patterns.

**Algorithm**:
1. Get last 3 distances from distance deque
2. Get oldest distance for comparison
3. Calculate corresponding wheel positions
4. Categorize by dozens and zeros
5. Return categorized unhit numbers

**Categories**:
- **Zs**: Zero and double-zero (0, 00)
- **D1**: First dozen (1-12)
- **D2**: Second dozen (13-24)
- **D3**: Third dozen (25-36)

#### 7.1.4 Distilled Output Logic
**Purpose**: Identify numbers that are strategically important but not recently hit.

**Calculation**:
```python
def calculate_distilled_output(tracker_unhit_cat, hitting_set_cat):
    distilled = {'Zs': [], 'D1': [], 'D2': [], 'D3': []}
    tracker_seen = {num for cat_nums in tracker_unhit_cat.values()
                   for num in cat_nums}

    for cat_key, hs_nums in hitting_set_cat.items():
        dist_cat_nums = [num for num in hs_nums
                        if num not in tracker_seen]
        distilled[cat_key] = dist_cat_nums

    return distilled, total_count
```

**Business Interpretation**:
- Numbers in hitting set but not in recent unhit patterns
- Represents least likely numbers based on mathematical analysis
- Used for win/loss streak tracking
- Announced via text-to-speech for real-time feedback

### 7.2 Statistical Analysis

#### 7.2.1 Win/Loss Streak Tracking
**Purpose**: Monitor effectiveness of distilled output predictions.

**Win Definition**: Distilled number does NOT appear in next entry
**Loss Definition**: Distilled number DOES appear in next entry

**Tracking Metrics**:
- Current win streak
- Current loss streak
- Maximum win streak achieved
- Maximum loss streak achieved
- Total wins in session
- Average numbers between wins

**Update Logic**:
```python
def update_win_loss_streaks(admin_s_d, is_match):
    if is_match:  # Loss - distilled number came up
        current_loss_streak += 1
        current_win_streak = 0
        max_loss_streak = max(max_loss_streak, current_loss_streak)
        spins_since_last_match = 0
    else:  # Win - distilled number didn't come up
        current_win_streak += 1
        current_loss_streak = 0
        max_win_streak = max(max_win_streak, current_win_streak)
        total_wins += 1
        sum_spins_between_wins += spins_since_last_match
        spins_since_last_match += 1
```

#### 7.2.2 Performance Metrics
**Average Calculation**:
```python
avg_spins_between_wins = (sum_of_spins_between_wins / total_wins)
                        if total_wins > 0 else 0.0
```

**Status Indicators**:
- **RED**: ≤70 entries (requires attention)
- **YELLOW**: 71-90 entries (moderate activity)
- **GREEN**: >90 entries (sufficient data)

### 7.3 Session State Management

#### 7.3.1 State Transitions
**Session Creation**:
1. Admin login creates new session
2. Initialize RouletteTracker with default wheel type
3. Set up empty statistics and connections
4. Generate unique session identifier

**Number Entry Process**:
1. Validate number against current wheel type
2. Calculate previous distilled output for comparison
3. Update tracker with new number(s)
4. Check for match against previous distilled output
5. Update win/loss streaks
6. Broadcast state update to all clients
7. Trigger text-to-speech if new distilled output

**Session Reset**:
1. Save current session history to file
2. Reset tracker and statistics
3. Clear previous distilled output state
4. Maintain user connections and session metadata

**Wheel Type Change**:
1. Save current session with old wheel type
2. Create new tracker with new wheel type
3. Reset all statistics and state
4. Update all connected clients

#### 7.3.2 Data Consistency
**Undo Operations**:
1. Remove last entry from history
2. Rebuild entire tracker state from remaining history
3. Recalculate all distances and statistics
4. Handle edge cases (empty history, calculation errors)
5. Rollback on any errors to maintain consistency

**Error Recovery**:
- Validate all inputs before state changes
- Use transactions for multi-step operations
- Maintain backup state during critical operations
- Log all state changes for debugging

### 7.4 Real-time Synchronization

#### 7.4.1 State Broadcasting
**Trigger Events**:
- Number entry (admin or user)
- Undo operation
- Session reset
- Wheel type change
- User connection/disconnection

**State Package**:
```python
def get_admin_dashboard_state(admin_s_data):
    return {
        "status": status_indicator,
        "entryCount": entry_count,
        "saveTarget": save_file_display_name,
        "lastSpin": last_number_entered,
        "spinHistoryCount": history_length,
        "spinHistoryList": number_history,
        "previousDistillOutput": previous_distill_state,
        "distillOutput": current_distill_state,
        "spinsSinceLastMatch": numbers_since_match,
        "maxWinStreak": max_wins,
        "maxLossStreak": max_losses,
        "avgSpinsBetweenWins": average_between_wins,
        "connectedUserCount": user_count,
        "adminUsername": admin_name,
        "currentWheelType": wheel_type,
        "ttsData": text_to_speech_data
    }
```

#### 7.4.2 Conflict Resolution
**Concurrent Updates**:
- All state changes processed sequentially
- WebSocket events queued and processed in order
- Session validation before each operation
- Automatic reconnection handling for clients

**User Permissions**:
- Admin: Full control over session
- Users: Submit numbers and undo only
- Validation of user permissions on each request
- Automatic disconnection of unauthorized users

## 8. Security Requirements

### 8.1 Authentication Security

#### 8.1.1 Password Security
**Requirements**:
- Passwords hashed using Werkzeug's secure methods
- No plaintext password storage
- Secure password comparison using timing-safe functions
- Default admin password must be changed in production

**Implementation**:
```python
# Password hashing
hashed_password = generate_password_hash("password")

# Password verification
is_valid = check_password_hash(hashed_password, provided_password)
```

#### 8.1.2 Session Security
**Session Token Requirements**:
- Cryptographically secure random generation
- 32-character hexadecimal tokens (128-bit entropy)
- Secure cookie configuration
- SameSite=Lax for CSRF protection

**Session Management**:
- Automatic timeout after 2 hours of inactivity
- Activity timestamp updates on valid requests
- Secure session cleanup on logout
- Protection against session fixation attacks

### 8.2 Input Validation

#### 8.2.1 Number Validation
**Requirements**:
- Validate all number inputs against wheel type
- Sanitize input strings before processing
- Prevent injection attacks through number fields
- Handle multi-number input securely

**Validation Rules**:
- American wheel: 0, 00, 1-36 only
- European wheel: 0, 1-36 only
- Hyphen-separated format for multiple numbers
- Maximum reasonable input length limits

#### 8.2.2 File Path Security
**Requirements**:
- Validate all file operations against directory traversal
- Sanitize filenames using regex patterns
- Prevent access outside designated directories
- Validate file existence and permissions

**Implementation**:
```python
# Filename validation
safe_filename = os.path.basename(filename)
if not re.match(r'^session_[a-zA-Z0-9_-]+\d{8}_\d{6}_\d{6}_[a-zA-Z0-9_-]+\.txt$',
                safe_filename):
    return error_response("Invalid filename format")

# Path validation
if os.path.commonprefix((os.path.realpath(file_path),
                        os.path.realpath(SESSION_HISTORY_DIRECTORY))) != \
   os.path.realpath(SESSION_HISTORY_DIRECTORY):
    return error_response("Access denied")
```

### 8.3 Rate Limiting

#### 8.3.1 Login Protection
**Requirements**:
- Maximum 5 login attempts per IP per 5 minutes
- Sliding window rate limiting
- Clear error messages for rate limit violations
- Automatic cleanup of rate limit data

#### 8.3.2 Code Generation Protection
**Requirements**:
- Maximum 10 code generation requests per IP per minute
- Prevent code enumeration attacks
- Automatic cleanup of expired codes
- Audit logging of all code generation attempts

### 8.4 Data Protection

#### 8.4.1 Audit Logging
**Requirements**:
- Log all security-relevant events
- Include timestamps, IP addresses, and user identifiers
- Structured JSON format for analysis
- Rotating log files to prevent disk overflow
- Secure log file permissions

**Logged Events**:
- Login attempts (success/failure)
- Code generation and validation
- Session creation and cleanup
- File operations (view/delete)
- Administrative actions
- Security violations

#### 8.4.2 Error Handling
**Requirements**:
- No sensitive information in error messages
- Generic error responses for security events
- Detailed logging for debugging without exposure
- Graceful degradation on security failures

**Error Response Strategy**:
- Authentication errors: Generic "invalid credentials" message
- Authorization errors: Generic "unauthorized" message
- Rate limiting: Clear indication of limits and retry timing
- System errors: Generic message with detailed logging

## 9. Performance Requirements

### 9.1 Response Time Requirements

#### 9.1.1 Page Load Performance
**Requirements**:
- Initial page load: <2 seconds on 3G connection
- Admin dashboard load: <3 seconds with full state
- User portal load: <1.5 seconds
- Static asset caching for repeat visits
- Progressive loading for large datasets

**Optimization Strategies**:
- Minified CSS and JavaScript
- Efficient DOM manipulation
- Lazy loading for non-critical components
- CDN usage for external libraries (SocketIO)

#### 9.1.2 Real-time Communication
**Requirements**:
- WebSocket connection establishment: <500ms
- State update propagation: <100ms
- Number entry processing: <50ms
- Undo operation completion: <200ms
- Cross-client synchronization: <150ms

**Performance Monitoring**:
- Connection latency tracking
- Message processing time measurement
- Client-side performance metrics
- Server-side response time logging

### 9.2 Scalability Targets

#### 9.2.1 Concurrent Users
**Current Capacity**:
- 1 admin session with up to 10 concurrent users
- Multiple admin sessions supported simultaneously
- WebSocket connection pooling
- Memory-efficient session storage

**Scaling Considerations**:
- Session data stored in memory (not persistent across restarts)
- File-based history storage (local filesystem)
- Single-server deployment model
- Horizontal scaling would require session store migration

#### 9.2.2 Data Volume Handling
**Session History**:
- Maximum 100 saved sessions in metadata
- Individual session files up to 10MB
- Automatic cleanup of old sessions
- Efficient file I/O operations

**Memory Management**:
- Session cleanup after 2 hours inactivity
- Periodic garbage collection of expired data
- Efficient data structures (deque for distances)
- Memory usage monitoring and alerts

### 9.3 Caching Strategy

#### 9.3.1 Client-Side Caching
**Static Assets**:
- CSS and JavaScript files with cache headers
- Font files cached for extended periods
- Image assets with appropriate cache policies
- Service worker for offline functionality (future enhancement)

**Application State**:
- Local storage for user preferences
- Session storage for temporary data
- Browser cache for API responses where appropriate

#### 9.3.2 Server-Side Optimization
**Computation Caching**:
- Hitting set calculations cached per session state
- Distilled output cached until state changes
- Wheel sequence data as class constants
- Efficient algorithm implementations

**Database Optimization**:
- File-based storage with efficient indexing
- JSON metadata for fast session lookups
- Minimal disk I/O for routine operations
- Batch operations for bulk data handling

## 10. Error Handling

### 10.1 Client-Side Error Handling

#### 10.1.1 Network Error Management
**Connection Failures**:
- Automatic WebSocket reconnection attempts
- Exponential backoff for retry logic
- User notification of connection status
- Graceful degradation when offline

**API Error Responses**:
- Standardized error message display
- User-friendly error descriptions
- Retry mechanisms for transient failures
- Fallback options for critical operations

#### 10.1.2 Input Validation Errors
**Form Validation**:
- Real-time validation feedback
- Clear error messages for invalid inputs
- Prevention of invalid form submission
- Accessibility-compliant error announcements

**Number Entry Errors**:
- Immediate feedback for invalid numbers
- Format guidance for multi-number entries
- Wheel-specific validation messages
- Suggestion of valid alternatives

### 10.2 Server-Side Error Handling

#### 10.2.1 Application Errors
**RouletteTracker Errors**:
- Invalid number validation with descriptive messages
- State corruption detection and recovery
- Undo operation failure handling
- Wheel sequence validation

**Session Management Errors**:
- Session timeout handling with user notification
- Invalid session detection and cleanup
- Concurrent access conflict resolution
- Memory overflow protection

#### 10.2.2 System-Level Errors
**File System Errors**:
- Disk space monitoring and alerts
- File permission error handling
- Corrupted file detection and recovery
- Backup and restore procedures

**Network Errors**:
- WebSocket connection failure recovery
- Client disconnection handling
- Message delivery failure detection
- Connection pool management

### 10.3 Error Recovery Procedures

#### 10.3.1 Automatic Recovery
**Session Recovery**:
- Automatic session state reconstruction
- History rebuild from saved data
- Statistics recalculation on corruption
- User reconnection handling

**Data Recovery**:
- Backup creation before critical operations
- Rollback procedures for failed operations
- Consistency checks after state changes
- Automatic cleanup of corrupted data

#### 10.3.2 Manual Recovery
**Administrative Tools**:
- Session history repair utilities
- Manual session cleanup procedures
- Log file analysis tools
- Performance monitoring dashboards

**User Recovery Options**:
- Session reset functionality
- Manual data entry for lost information
- Export/import capabilities for session data
- Contact support procedures

## 11. Testing Strategy

### 11.1 Unit Testing

#### 11.1.1 Core Algorithm Testing
**RouletteTracker Class**:
```python
def test_number_validation():
    # Test valid numbers for both wheel types
    # Test invalid number rejection
    # Test multi-number input processing
    # Test edge cases (empty input, special characters)

def test_distance_calculation():
    # Test distance formula accuracy
    # Test wrap-around calculations
    # Test consecutive number processing
    # Test undo operation correctness

def test_hitting_set_algorithm():
    # Test minimal set generation
    # Test coverage completeness
    # Test randomization effects
    # Test performance with large datasets
```

**Statistical Functions**:
```python
def test_win_loss_tracking():
    # Test streak calculation accuracy
    # Test average computation
    # Test edge cases (no wins, no losses)
    # Test state reset behavior

def test_distilled_output():
    # Test categorization accuracy
    # Test filtering logic
    # Test empty set handling
    # Test category sorting
```

#### 11.1.2 Utility Function Testing
**Security Functions**:
```python
def test_rate_limiting():
    # Test limit enforcement
    # Test window sliding behavior
    # Test cleanup procedures
    # Test concurrent access handling

def test_session_management():
    # Test session creation and cleanup
    # Test timeout handling
    # Test validation procedures
    # Test security token generation
```

### 11.2 Integration Testing

#### 11.2.1 API Endpoint Testing
**Authentication Endpoints**:
- Login with valid/invalid credentials
- Rate limiting enforcement
- Session creation and management
- Logout and cleanup procedures

**Session Management Endpoints**:
- Code generation and validation
- History file operations
- Permission enforcement
- Error response handling

#### 11.2.2 WebSocket Communication Testing
**Connection Management**:
- Client connection and disconnection
- Room joining and leaving
- Message broadcasting
- Error propagation

**Real-time Features**:
- State synchronization across clients
- Concurrent user operations
- Admin session management
- User notification systems

### 11.3 User Acceptance Testing

#### 11.3.1 Admin Workflow Testing
**Session Management**:
- Create new session and configure wheel type
- Enter numbers and verify tracking accuracy
- Generate user codes and manage connections
- Save and retrieve session histories
- Reset sessions and verify data preservation

**Advanced Features**:
- Text-to-speech functionality across browsers
- Multi-number entry with various formats
- Undo operations with state verification
- Statistical tracking accuracy
- Performance under load

#### 11.3.2 User Workflow Testing
**Basic Operations**:
- Join session using access code
- Submit numbers and verify acceptance
- Use undo functionality
- Handle connection interruptions
- Receive real-time feedback

**Error Scenarios**:
- Invalid access codes
- Expired sessions
- Network disconnections
- Invalid number submissions
- Admin session termination

### 11.4 Performance Testing

#### 11.4.1 Load Testing
**Concurrent Users**:
- Multiple users in single session
- Multiple admin sessions simultaneously
- WebSocket connection limits
- Memory usage under load
- Response time degradation

**Data Volume Testing**:
- Large session histories (1000+ numbers)
- Multiple saved sessions
- File system performance
- Algorithm performance with large datasets

#### 11.4.2 Stress Testing
**Resource Limits**:
- Memory exhaustion scenarios
- Disk space limitations
- Network bandwidth constraints
- CPU intensive operations

**Recovery Testing**:
- Server restart scenarios
- Database corruption recovery
- Network partition handling
- Client reconnection behavior

## 12. Deployment Specifications

### 12.1 Environment Requirements

#### 12.1.1 System Requirements
**Operating System**:
- Linux (Ubuntu 20.04+ recommended)
- Python 3.8+ with pip
- Virtual environment support
- Systemd for service management

**Hardware Requirements**:
- Minimum: 1 CPU core, 512MB RAM, 1GB disk
- Recommended: 2 CPU cores, 2GB RAM, 10GB disk
- Network: Stable internet connection
- Ports: 8081 (configurable)

#### 12.1.2 Software Dependencies
**Python Packages**:
```
Flask>=2.0
Flask-SocketIO>=5.0
Werkzeug>=2.0
python-dotenv
eventlet
```

**System Packages**:
- Python3 development headers
- Build tools for native extensions
- SSL/TLS certificates for HTTPS (production)

### 12.2 Installation Procedures

#### 12.2.1 Initial Setup
```bash
# Create application directory
mkdir -p /home/<USER>/Applications/app_1
cd /home/<USER>/Applications/app_1

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install --upgrade pip
pip install -r requirements.txt

# Create required directories
mkdir -p logs session_history

# Set file permissions
chmod 755 app.py
chmod 644 requirements.txt
```

#### 12.2.2 Service Configuration
**Systemd Service File** (`/etc/systemd/system/app_1.service`):
```ini
[Unit]
Description=Roulette Tracker Application
After=network.target

[Service]
User=appuser
WorkingDirectory=/home/<USER>/Applications/app_1
ExecStart=/home/<USER>/Applications/app_1/venv/bin/python3 /home/<USER>/Applications/app_1/app.py
Restart=always
RestartSec=10
Environment=PYTHONUNBUFFERED=1

[Install]
WantedBy=multi-user.target
```

**Service Management**:
```bash
# Enable and start service
sudo systemctl enable app_1.service
sudo systemctl start app_1.service

# Check status
sudo systemctl status app_1.service

# View logs
sudo journalctl -u app_1.service -f
```

### 12.3 Configuration Management

#### 12.3.1 Environment Variables
**Security Configuration**:
- `SECRET_KEY`: Flask secret key (auto-generated if not set)
- `ADMIN_PASSWORD`: Default admin password (change in production)
- `SESSION_TIMEOUT`: Session timeout in hours (default: 2)
- `DEBUG_MODE`: Enable/disable debug mode (default: False)

**Application Configuration**:
- `HOST`: Bind address (default: 0.0.0.0)
- `PORT`: Listen port (default: 8081)
- `LOG_LEVEL`: Logging level (default: INFO)
- `MAX_SESSIONS`: Maximum saved sessions (default: 100)

#### 12.3.2 Production Hardening
**Security Measures**:
- Change default admin credentials
- Use HTTPS with valid SSL certificates
- Configure firewall rules
- Enable audit logging
- Set up log rotation
- Configure backup procedures

**Performance Optimization**:
- Use production WSGI server (Gunicorn)
- Configure reverse proxy (Nginx)
- Enable gzip compression
- Set up monitoring and alerting
- Configure log aggregation

### 12.4 Monitoring and Maintenance

#### 12.4.1 Health Monitoring
**Application Metrics**:
- Active session count
- WebSocket connection count
- Memory usage
- Response times
- Error rates

**System Metrics**:
- CPU utilization
- Memory usage
- Disk space
- Network connectivity
- Service uptime

#### 12.4.2 Backup and Recovery
**Data Backup**:
- Session history files
- Application logs
- Configuration files
- Database/metadata files

**Recovery Procedures**:
- Service restart procedures
- Data restoration from backups
- Configuration rollback
- Emergency contact procedures

**Maintenance Tasks**:
- Log file rotation and cleanup
- Session history archival
- Security updates
- Performance optimization
- Capacity planning

---

## Conclusion

This Product Requirements Document provides comprehensive specifications for recreating the Roulette Tracker application from scratch. The document covers all aspects of the system including technical architecture, feature specifications, user interface design, security requirements, and deployment procedures.

The application represents a sophisticated real-time collaborative platform with advanced mathematical algorithms, comprehensive security measures, and a responsive user interface. Implementation should follow the specifications exactly to ensure compatibility and functionality equivalent to the original system.

For questions or clarifications regarding any aspect of this specification, refer to the original codebase or contact the development team.
```